# pyright: standard
"""
测试优化后的MotorController类功能

该测试文件验证：
1. 电机参数在初始化时的正确设置
2. 零位校准功能的正常工作
3. 编码器/光栅尺检测功能
4. 轴状态查询功能
"""

import pytest
import time
from sweeper400.move.move import MotorController


def test_motor_controller_initialization():
    """测试电机控制器初始化参数设置"""
    print("\n=== 测试电机控制器初始化 ===")

    # 测试默认参数初始化
    controller1 = MotorController()
    params1 = controller1.get_motor_parameters()
    print(f"默认参数: {params1}")

    assert params1["step_angle"] == 1.8
    assert params1["subdivision"] == 20
    assert params1["pitch"] == 4.0
    assert params1["transmission_ratio"] == 1.0

    # 测试自定义参数初始化
    controller2 = MotorController(
        step_angle=0.9, subdivision=32, pitch=2.0, transmission_ratio=2.0
    )
    params2 = controller2.get_motor_parameters()
    print(f"自定义参数: {params2}")

    assert params2["step_angle"] == 0.9
    assert params2["subdivision"] == 32
    assert params2["pitch"] == 2.0
    assert params2["transmission_ratio"] == 2.0

    print("√ 初始化参数测试通过")


def test_motor_controller_parameter_update():
    """测试电机参数更新功能"""
    print("\n=== 测试电机参数更新 ===")

    controller = MotorController()

    # 更新部分参数
    controller.update_motor_parameters(step_angle=1.2, subdivision=16)
    params = controller.get_motor_parameters()
    print(f"更新后参数: {params}")

    assert params["step_angle"] == 1.2
    assert params["subdivision"] == 16
    assert params["pitch"] == 4.0  # 保持原值
    assert params["transmission_ratio"] == 1.0  # 保持原值

    print("√ 参数更新测试通过")


def test_unit_conversion():
    """测试单位换算功能"""
    print("\n=== 测试单位换算功能 ===")

    controller = MotorController()

    # 测试换算系数计算
    factor = controller.get_conversion_factor()
    print(f"换算系数: {factor:.2f} 步/mm")

    # 测试距离转脉冲数
    distance = 10.0  # 10mm
    steps = controller.mm_to_steps(distance)
    print(f"{distance}mm = {steps}步")

    # 测试脉冲数转距离
    back_distance = controller.steps_to_mm(steps)
    print(f"{steps}步 = {back_distance:.3f}mm")

    # 验证往返换算精度
    assert abs(distance - back_distance) < 0.001

    print("√ 单位换算测试通过")


def test_motor_controller_connection():
    """测试电机控制器连接功能"""
    print("\n=== 测试电机控制器连接 ===")

    controller = MotorController()

    # 初始化API
    init_success = controller.initialize()
    print(f"API初始化: {'成功' if init_success else '失败'}")

    if init_success:
        # 尝试USB连接
        connect_success = controller.connect_usb()
        print(f"USB连接: {'成功' if connect_success else '失败'}")

        if connect_success:
            # 获取硬件信息
            hw_info = controller.get_hardware_info()
            print(f"硬件信息: {hw_info}")

            # 检查编码器可用性
            encoder_available = controller.check_encoder_available()
            print(f"编码器/光栅尺可用: {'是' if encoder_available else '否'}")

            return controller, True
        else:
            print("⚠ USB连接失败，可能设备未连接")
            return controller, False
    else:
        print("⚠ API初始化失败")
        return controller, False


def test_axis_status():
    """测试轴状态查询功能"""
    print("\n=== 测试轴状态查询 ===")

    controller, connected = test_motor_controller_connection()

    if connected:
        # 查询X轴状态
        x_status = controller.get_axis_status(0)
        print(f"X轴状态: {x_status}")

        # 查询Y轴状态
        y_status = controller.get_axis_status(1)
        print(f"Y轴状态: {y_status}")

        print("√ 轴状态查询测试完成")
    else:
        print("⚠ 跳过轴状态查询测试（未连接）")


def test_home_calibration():
    """测试零位校准功能"""
    print("\n=== 测试零位校准功能 ===")

    controller, connected = test_motor_controller_connection()

    if connected:
        print("准备开始零位校准...")
        print("请确认：")
        print("1. 电机已正确连接")
        print("2. 运动范围内无障碍物")
        print("3. 限位开关或编码器零位已正确安装")

        # 询问用户是否继续
        user_input = input("\n是否继续进行零位校准？(y/N): ").strip().lower()

        if user_input == "y":
            print("\n开始X轴零位校准...")

            # 执行X轴零位校准
            x_calibration_success = controller.home_calibration(
                axis=0,
                home_speed=-5000,  # 较慢的速度以提高精度
                acceleration=1000,
                deceleration=1000,
                timeout_seconds=60.0,
            )

            print(f"X轴零位校准: {'成功' if x_calibration_success else '失败'}")

            if x_calibration_success:
                # 查看校准后的状态
                x_status = controller.get_axis_status(0)
                print(f"X轴校准后状态: {x_status}")

                # 询问是否校准Y轴
                user_input = input("\n是否继续进行Y轴零位校准？(y/N): ").strip().lower()

                if user_input == "y":
                    print("\n开始Y轴零位校准...")

                    y_calibration_success = controller.home_calibration(
                        axis=1,
                        home_speed=-5000,
                        acceleration=1000,
                        deceleration=1000,
                        timeout_seconds=60.0,
                    )

                    print(f"Y轴零位校准: {'成功' if y_calibration_success else '失败'}")

                    if y_calibration_success:
                        y_status = controller.get_axis_status(1)
                        print(f"Y轴校准后状态: {y_status}")

            print("√ 零位校准测试完成")
        else:
            print("⚠ 跳过零位校准测试（用户取消）")
    else:
        print("⚠ 跳过零位校准测试（未连接）")


def main():
    """主测试函数"""
    print("开始测试优化后的MotorController类...")

    # 基础功能测试（不需要硬件连接）
    test_motor_controller_initialization()
    test_motor_controller_parameter_update()
    test_unit_conversion()

    # 硬件相关测试（需要硬件连接）
    test_axis_status()
    test_home_calibration()

    print("\n=== 所有测试完成 ===")


y
if __name__ == "__main__":
    main()
