# pyright: standard
"""
专门测试零位校准功能的脚本

该脚本用于测试优化后的MotorController类的零位校准功能
"""

from sweeper400.move.move import MotorController
import time


def test_home_calibration():
    """测试零位校准功能"""
    print("=== 零位校准功能测试 ===")

    # 创建控制器实例
    controller = MotorController()
    print(f"电机参数: {controller.get_motor_parameters()}")

    # 初始化和连接
    if not controller.initialize():
        print("API初始化失败")
        return False

    if not controller.connect_usb():
        print("USB连接失败")
        return False

    print("控制器连接成功")

    # 获取硬件信息
    hw_info = controller.get_hardware_info()
    print(f"硬件信息: {hw_info}")

    # 检查编码器可用性
    encoder_available = controller.check_encoder_available()
    print(f"编码器/光栅尺可用: {'是' if encoder_available else '否'}")

    # 查看初始状态
    print("\n--- 初始状态 ---")
    x_status = controller.get_axis_status(0)
    y_status = controller.get_axis_status(1)
    print(f"X轴状态: {x_status}")
    print(f"Y轴状态: {y_status}")

    print("\n准备进行X轴零位校准...")
    print("请确认电机可以安全运动，然后按Enter继续...")
    input()

    # 执行X轴零位校准
    print("开始X轴零位校准...")
    x_success = controller.home_calibration(
        axis=0,
        home_speed=-10000,  # 负向，较慢速度
        acceleration=2000,
        deceleration=2000,
        timeout_seconds=60.0,
    )

    print(f"X轴零位校准结果: {'成功' if x_success else '失败'}")

    # 查看校准后状态
    print("\n--- X轴校准后状态 ---")
    x_status = controller.get_axis_status(0)
    print(f"X轴状态: {x_status}")

    if x_success:
        print("\nX轴校准成功！是否继续Y轴校准？(y/N): ", end="")
        user_input = input().strip().lower()

        if user_input == "y":
            print("开始Y轴零位校准...")
            y_success = controller.home_calibration(
                axis=1,
                home_speed=-10000,
                acceleration=2000,
                deceleration=2000,
                timeout_seconds=60.0,
            )

            print(f"Y轴零位校准结果: {'成功' if y_success else '失败'}")

            # 查看Y轴校准后状态
            print("\n--- Y轴校准后状态 ---")
            y_status = controller.get_axis_status(1)
            print(f"Y轴状态: {y_status}")

            if y_success:
                print("\n--- 最终状态 ---")
                x_status = controller.get_axis_status(0)
                y_status = controller.get_axis_status(1)
                print(f"X轴最终状态: {x_status}")
                print(f"Y轴最终状态: {y_status}")

    # 清理资源
    controller.cleanup()
    print("\n零位校准测试完成")
    return x_success


if __name__ == "__main__":
    test_home_calibration()
